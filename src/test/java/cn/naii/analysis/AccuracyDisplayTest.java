package cn.naii.analysis;

import cn.naii.analysis.calculator.AccuracyCalculator;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

/**
 * 测试准确率分析结果中不匹配项的显示
 */
public class AccuracyDisplayTest {
    
    public static void main(String[] args) {
        System.out.println("=== 测试准确率分析结果显示 ===");
        
        // 创建测试数据
        JSONObject predictObject = createTestPredictObject();
        JSONObject labelObject = createTestLabelObject();
        
        AccuracyCalculator calculator = new AccuracyCalculator();
        
        // 测试文档字段准确率计算
        System.out.println("1. 文档字段准确率计算:");
        StringBuilder documentAccuracy = calculator.calculateDocumentAccuracy(predictObject, labelObject, 0);
        System.out.println(documentAccuracy.toString());
        
        // 测试患者信息准确率计算（带详情）
        System.out.println("\n2. 患者信息准确率计算（带详情）:");
        String patientDetails = calculator.calculatePatientAccuracyWithDetails(predictObject, labelObject, 0);
        System.out.println(patientDetails);
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    private static JSONObject createTestPredictObject() {
        JSONObject predictObject = new JSONObject();
        predictObject.put("日期", "2024-01-01");
        
        JSONArray patientArray = new JSONArray();
        JSONObject patient = new JSONObject();
        patient.put("条形码", "123456");
        patient.put("患者姓名", "张三");
        patient.put("年龄", "30");
        patient.put("性别", "男");
        patient.put("住院/门诊号", "H001");
        patient.put("床位", "1床");
        patient.put("科室", "内科");
        patient.put("样本类型", "血液");
        patient.put("样本性状", "正常");
        patient.put("采集时间", "08:00");
        patient.put("医生", "李医生");
        patient.put("检测项目", "血常规");
        patient.put("备注栏", "");
        
        patientArray.add(patient);
        predictObject.put("患者信息", patientArray);
        
        return predictObject;
    }
    
    private static JSONObject createTestLabelObject() {
        JSONObject labelObject = new JSONObject();
        labelObject.put("日期", "2024-01-02"); // 不匹配的日期
        labelObject.put("图片地址", "C:\\test\\image.jpg");
        
        JSONArray patientArray = new JSONArray();
        JSONObject patient = new JSONObject();
        patient.put("条形码", "123456");
        patient.put("患者姓名", "张三");
        patient.put("年龄", "31"); // 不匹配的年龄
        patient.put("性别", "男");
        patient.put("住院/门诊号", "H002"); // 不匹配的住院号
        patient.put("床位", "床位1"); // 应该匹配（模糊匹配）
        patient.put("科室", "内科门诊"); // 应该匹配（包含匹配）
        patient.put("样本类型", "血液");
        patient.put("样本性状", "正常");
        patient.put("采集时间", "08:30"); // 应该匹配（只比较小时）
        patient.put("医生", "李医生");
        patient.put("检测项目", "血常规检查"); // 应该匹配（包含匹配）
        patient.put("备注栏", "无异常"); // 不匹配
        
        patientArray.add(patient);
        labelObject.put("患者信息", patientArray);
        
        return labelObject;
    }


}
