package cn.naii.analysis;


import cn.naii.analysis.matcher.FieldMatcher;
import com.alibaba.fastjson2.JSON;
import org.junit.jupiter.api.Test;

import java.util.Set;

public class UtilTest {
    @Test
    public void convertTestItemSetTest() {
        String item = "儿茶酚胺六项";
        System.out.println(JSON.toJSONString(FieldMatcher.getTestItemMatcher(item)));
    }

    @Test
    public void cleanTestItemStringTest() {
        String item = "骨标志物三项\n" +
                "25轻基维生素D2D3";
        System.out.println(FieldMatcher.TestItemMatcher.cleanTestItemString(item));
    }

    @Test
    public void allTest() {
//        String item = "骨标志物三项\n" +
//                "25羟基维生素D2D3";
        String item = "女性肿瘤物12项\n" +
                "（自建）";
        item = FieldMatcher.TestItemMatcher.cleanTestItemString(item);
        Set<String> set = FieldMatcher.getTestItemMatcher(item);
        System.out.println(JSON.toJSONString(set));
        System.out.println(String.join(",",set));
    }

    @Test
    public void regexTest() {
        String timeStr = "12时";
        String parseTimeString = FieldMatcher.parseTimeString(timeStr);
        System.out.println(parseTimeString);
    }
}
