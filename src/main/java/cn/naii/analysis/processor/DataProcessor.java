package cn.naii.analysis.processor;

import cn.hutool.core.util.StrUtil;
import cn.naii.analysis.calculator.AccuracyCalculator;
import cn.naii.analysis.config.AppConfig;
import cn.naii.analysis.model.AccuracyResult;
import cn.naii.analysis.model.PredictionData;
import cn.naii.analysis.util.JsonUtils;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;

/**
 * 数据处理器 - 负责处理预测数据的分析
 * 使用配置文件消除硬编码
 * <AUTHOR>
 */
@Getter
public class DataProcessor {

    /**
     * -- GETTER --
     *  获取准确率计算器
     */
    private final AccuracyCalculator accuracyCalculator;

    public DataProcessor() {
        this.accuracyCalculator = new AccuracyCalculator();
    }

    /**
     * 处理单个预测数据
     *
     * @param predictionData 预测数据
     * @param sampleIndex 样本索引
     * @return 处理结果字符串
     */
    public String processPredictionData(PredictionData predictionData, int sampleIndex) {
        try {
            // 清理和解析JSON
            String predictJson = JsonUtils.sanitizeJsonString(predictionData.getPredict());
            String labelJson = JsonUtils.sanitizeJsonString(predictionData.getLabel());
            predictJson = predictJson.replace("NaN", "null");
            labelJson = labelJson.replace("NaN", "null");

            JSONObject predictObject = JSONObject.parseObject(predictJson);
            JSONObject labelObject = JSONObject.parseObject(labelJson);

            predictObject = convert(predictObject);
            labelObject = convert(labelObject);
//            predictObject = TimeSplitter.splitCollectionTime(predictObject);
//            labelObject = TimeSplitter.splitCollectionTime(labelObject);

            return processJsonObjects(predictObject, labelObject, sampleIndex);

        } catch (Exception e) {
            return handleProcessingError(e, sampleIndex, predictionData);
        }
    }

    private JSONObject convert(JSONObject data){
        JSONArray jsonArray = data.getJSONArray("患者信息");
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject object = jsonArray.getJSONObject(i);
            if(StrUtil.isNotBlank(object.getString("日期"))){
                object.put("采集日期",object.getString("日期"));
            }
            if (StrUtil.isNotBlank(object.getString("姓名"))) {
                object.put("患者姓名",object.getString("姓名"));
            }
            if (StrUtil.isNotBlank(object.getString("送检医生"))) {
                object.put("医生",object.getString("送检医生"));
            }
        }
        return data;
    }



    /**
     * 处理JSON对象
     */
    private String processJsonObjects(JSONObject predictObject, JSONObject labelObject, int sampleIndex) {
        StringBuilder result = new StringBuilder();

        // 打印样本编号 - 使用配置的模板
        result.append("\n").append(AppConfig.formatMessage("output.sample.header.template", sampleIndex + 1));

        // 输出基本信息
        String labelInfo = formatBasicInfo(AppConfig.getLabelInfoTemplate(), labelObject);
        String predictInfo = formatBasicInfo(AppConfig.getPredictInfoTemplate(), predictObject);
        result.append("\n").append(labelInfo);
        result.append("\n").append(predictInfo);

        // 计算准确率
        StringBuilder accuracyDetails = new StringBuilder();

        // 计算文档字段的准确率
//        StringBuilder documentAccuracy = accuracyCalculator.calculateDocumentAccuracy(predictObject, labelObject, sampleIndex);
//        accuracyDetails.append(documentAccuracy);

        // 计算患者信息的准确率并获取不匹配详情
        String patientAccuracyDetails = accuracyCalculator.calculatePatientAccuracyWithDetails(predictObject, labelObject, sampleIndex);
        accuracyDetails.append(patientAccuracyDetails);

        // 计算患者信息的准确率统计
        AccuracyResult accuracyResult = accuracyCalculator.calculatePatientAccuracy(predictObject, labelObject, sampleIndex);

        // 输出准确率结果 - 使用配置的标题
        result.append("\n\n").append(AppConfig.getAccuracyAnalysisHeader());
        result.append("\n").append(accuracyDetails);
        result.append("\n").append(accuracyResult.toString());

        return result.toString();
    }

    /**
     * 格式化基本信息 - 使用配置的字段名称和分隔符
     */
    private String formatBasicInfo(String label, JSONObject jsonObject) {
        StringBuilder sb = new StringBuilder();

        String dateFieldName = AppConfig.getProperty("field.date", "日期");
        String imageFieldName = AppConfig.getProperty("field.image.path", "图片地址");
        String dateValue = JsonUtils.getStringValue(jsonObject, dateFieldName);

        if (AppConfig.getLabelInfoTemplate().equals(label)) {
            // 标签对象包含图片地址
            String image = JsonUtils.getStringValue(jsonObject, imageFieldName);
            if (image != null) {
                String windowsSeparator = AppConfig.getWindowsPathSeparator();
                String extensionSeparator = AppConfig.getFileExtensionSeparator();
                image = image.substring(image.lastIndexOf(windowsSeparator) + 1, image.lastIndexOf(extensionSeparator));
                sb.append(label).append("图片地址: ").append(image).append(" 日期: ").append(dateValue);
            } else {
                sb.append(label).append(" 日期: ").append(dateValue);
            }
        } else {
            // 预测对象只包含日期字段
            sb.append(label).append(" 日期: ").append(dateValue);
        }

        // 格式化患者信息
        sb.append(formatPatientInfo(jsonObject));

        return sb.toString();
    }

    /**
     * 格式化患者信息 - 使用配置的字段名称
     */
    private String formatPatientInfo(JSONObject jsonObject) {
        StringBuilder sb = new StringBuilder();
        sb.append("\n【");

        String patientInfoFieldName = AppConfig.getJsonFieldPatientInfo();
        var jsonArray = jsonObject.getJSONArray(patientInfoFieldName);
        if (jsonArray != null) {
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject patient = jsonArray.getJSONObject(i);

                // 使用配置的字段名称
                String barcode = JsonUtils.getStringValue(patient, AppConfig.getProperty("field.barcode", "条形码"));
                String name = JsonUtils.getStringValue(patient, AppConfig.getProperty("field.patient.name", "患者姓名"));
                String age = JsonUtils.getStringValue(patient, AppConfig.getProperty("field.age", "年龄"));
                String gender = JsonUtils.getStringValue(patient, AppConfig.getProperty("field.gender", "性别"));
                String hospitalNumber = JsonUtils.getStringValue(patient, AppConfig.getProperty("field.hospital.number", "住院/门诊号"));
                String bedNumber = JsonUtils.getStringValue(patient, AppConfig.getProperty("field.bed.number", "床位"));
                String department = JsonUtils.getStringValue(patient, AppConfig.getProperty("field.department", "科室"));
                String region = JsonUtils.getStringValue(patient, AppConfig.getProperty("field.region", "病区"));
                String collectionDate = JsonUtils.getStringValue(patient, AppConfig.getProperty("field.collection.date", "采集日期"));
                String collectionTime = JsonUtils.getStringValue(patient, AppConfig.getProperty("field.collection.time", "采集时间"));
                String doctor = JsonUtils.getStringValue(patient, AppConfig.getProperty("field.doctor", "医生"));
                String testItem = JsonUtils.getStringValue(patient, AppConfig.getProperty("field.test.item", "检测项目"));
                String remarks = JsonUtils.getStringValue(patient, AppConfig.getProperty("field.remarks", "备注栏"));
                String sampleType = JsonUtils.getStringValue(patient, AppConfig.getProperty("field.sample.type", "样本类型"));
                String sampleStatus = JsonUtils.getStringValue(patient, AppConfig.getProperty("field.sample.status", "样本性状"));

                String patientInfo = "条形码: " + barcode + "|" +
                        "患者姓名: " + name + " |" +
                        "年龄: " + age + " |" +
                        "性别: " + gender + " |" +
                        "住院/门诊号: " + hospitalNumber + " |" +
                        "床位: " + bedNumber + " |" +
                        "科室: " + department + " |" +
                        "病区: " + region + " |" +
                        "采集日期: " + collectionDate + " |" +
                        "采集时间: " + collectionTime + " |" +
                        "医生: " + doctor + " |" +
                        "样本类型: " + sampleType + " |" +
                        "样本性状: " + sampleStatus + " |" +
                        "检测项目: " + testItem + " |" +
                        "备注栏: " + remarks;

                sb.append("\n").append(patientInfo);
            }
        }

        sb.append("】\n");
        return sb.toString();
    }

    /**
     * 处理错误情况 - 使用配置的错误消息模板
     */
    private String handleProcessingError(Exception e, int sampleIndex, PredictionData predictionData) {
        StringBuilder result = new StringBuilder();
        result.append("\n").append(AppConfig.formatMessage("output.error.sample.header.template", sampleIndex + 1));
        result.append("\n").append(AppConfig.formatMessage("error.json.parse", e.getMessage()));
        result.append("\n").append(AppConfig.getJsonFieldPredict()).append(" ").append(predictionData.getPredict());
        result.append("\n").append(AppConfig.getJsonFieldLabel()).append(" ").append(predictionData.getLabel());
        return result.toString();
    }

}
