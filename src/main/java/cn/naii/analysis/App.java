package cn.naii.analysis;

import cn.naii.analysis.config.AppConfig;
import cn.naii.analysis.matcher.FieldMatcher;
import cn.naii.analysis.model.PredictionData;
import cn.naii.analysis.processor.DataProcessor;
import cn.naii.analysis.reporter.AccuracyReporter;
import cn.naii.analysis.service.JsonFileReader;

import java.util.List;


/**
 * Application to read and process JSON prediction data
 * 重构后的主应用类，使用模块化设计和配置文件消除硬编码
 */
public class App {

    public static void main(String[] args) {
        // 从配置文件获取数据文件路径
        String filePath = AppConfig.getDataFilePath();

        // 创建组件实例
        JsonFileReader jsonFileReader = new JsonFileReader();
        DataProcessor dataProcessor = new DataProcessor();
        AccuracyReporter accuracyReporter = new AccuracyReporter();

        // 读取预测数据
        List<PredictionData> predictionDataList = jsonFileReader.readPredictionData(filePath);

        // 打印结果 - 使用配置的消息模板
        System.out.println(AppConfig.formatMessage("stats.total.predictions", predictionDataList.size()));
        System.out.println("\n" + AppConfig.getProperty("stats.sample.predictions", "Sample of predictions:"));

        // 处理每个样本
        for (int i = 0; i < predictionDataList.size(); i++) {
            PredictionData predictionData = predictionDataList.get(i);
            String result = dataProcessor.processPredictionData(predictionData, i);
            System.out.println(result);
        }

        // 生成完整的准确率报告
        String completeReport = accuracyReporter.generateCompleteReport(
                dataProcessor.getAccuracyCalculator().getGlobalFieldAccuracies(),
                dataProcessor.getAccuracyCalculator().getGlobalAccuracy(),
                dataProcessor.getAccuracyCalculator().getGlobalMatchedComparisons(),
                dataProcessor.getAccuracyCalculator().getGlobalTotalComparisons()
        );

        System.out.println(completeReport);

        // 检测项目标注错误详情
        System.out.println("检测项目标注错误数量"+FieldMatcher.errorTestItem.size()+"详情：");
        System.out.println(FieldMatcher.errorTestItem.toJSONString());
    }
}
