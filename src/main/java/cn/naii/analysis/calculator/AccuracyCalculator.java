package cn.naii.analysis.calculator;

import cn.naii.analysis.config.FieldConfig;
import cn.naii.analysis.matcher.FieldMatcher;
import cn.naii.analysis.model.AccuracyResult;
import cn.naii.analysis.model.FieldAccuracy;
import cn.naii.analysis.model.SampleAccuracy;
import cn.naii.analysis.util.JsonUtils;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * 准确率计算器
 */
public class AccuracyCalculator {

    // 全局统计
    private int allSamplesTotalComparisons = 0;
    private int allSamplesMatchedComparisons = 0;
    private final Map<String, FieldAccuracy> globalFieldAccuracies = new HashMap<>();

    public AccuracyCalculator() {
        initializeGlobalFieldAccuracies();
    }

    /**
     * 初始化全局字段准确率统计
     */
    private void initializeGlobalFieldAccuracies() {
        // 初始化患者信息字段
        for (String field : FieldConfig.getPatientFields()) {
            globalFieldAccuracies.put(field, new FieldAccuracy(field));
        }

        // 初始化文档字段
        for (String field : FieldConfig.getLabelDocumentFields()) {
            globalFieldAccuracies.put(field, new FieldAccuracy(field));
        }
    }

    /**
     * 计算文档字段的准确率
     */
    public StringBuilder calculateDocumentAccuracy(JSONObject predictObject, JSONObject labelObject, int sampleIndex) {
        StringBuilder sb = new StringBuilder();

        // 处理 predictObject 中的日期字段
        for (String field : FieldConfig.getPredictDocumentFields()) {
            String predictValue = JsonUtils.getStringValue(predictObject, field);
            String labelValue = JsonUtils.getStringValue(labelObject, field);

            processFieldComparison(field, labelValue, predictValue, sampleIndex, sb);
        }

        // 处理只在 labelObject 中存在的字段
        for (String field : FieldConfig.getLabelDocumentFields()) {
            // 跳过已经处理过的日期字段
            if ("日期".equals(field)) {
                continue;
            }

            String labelValue = JsonUtils.getStringValue(labelObject, field);

            // 只显示标签中的非空值，不进行匹配计算
            if (!JsonUtils.isEmptyValue(labelValue)) {
                if (!sb.toString().contains("文档字段匹配情况")) {
                    sb.append("\n文档字段匹配情况");
                }
                sb.append("\n    ").append(field).append(": ").append(labelValue).append(" vs ").append("空值(只在标签中存在)");
            }
        }

        return sb;
    }

    /**
     * 计算患者信息的准确率
     */
    public AccuracyResult calculatePatientAccuracy(JSONObject predictObject, JSONObject labelObject, int sampleIndex) {
        JSONArray jsonArrayLabel = labelObject.getJSONArray("患者信息");
        JSONArray jsonArrayPredict = predictObject.getJSONArray("患者信息");

        SampleAccuracy sampleAccuracy = new SampleAccuracy(sampleIndex);
        Map<String, FieldAccuracy> sampleFieldAccuracies = new HashMap<>();

        // 初始化样本字段统计
        String[] fields = FieldConfig.getPatientFields();
        for (String field : fields) {
            sampleFieldAccuracies.put(field, new FieldAccuracy(field));
        }

        // 按照顺序匹配患者信息
        int minSize = Math.min(jsonArrayLabel.size(), jsonArrayPredict.size());

        // 处理共同部分（按顺序一一对应）
        for (int i = 0; i < minSize; i++) {
            JSONObject labelPatient = jsonArrayLabel.getJSONObject(i);
            JSONObject predictPatient = jsonArrayPredict.getJSONObject(i);
            String labelName = labelPatient.getString("患者姓名");

            processPatientFields(fields, labelPatient, predictPatient, labelName, sampleIndex,
                    sampleAccuracy, sampleFieldAccuracies);
        }

        // 处理label数量多于predict的情况（多出的部分算作匹配失败）
        for (int i = minSize; i < jsonArrayLabel.size(); i++) {
            JSONObject labelPatient = jsonArrayLabel.getJSONObject(i);
            String labelName = labelPatient.getString("患者姓名");

            processUnmatchedPatient(fields, labelPatient, labelName, sampleIndex,
                    sampleAccuracy, sampleFieldAccuracies);
        }

        // 更新样本准确率
        for (FieldAccuracy fieldAccuracy : sampleFieldAccuracies.values()) {
            sampleAccuracy.addFieldAccuracy(fieldAccuracy.getFieldName(), fieldAccuracy);
        }

        // 计算各字段的准确率
        Map<String, Double> fieldAccuracies = new HashMap<>();
        for (String field : fields) {
            FieldAccuracy fieldAccuracy = sampleFieldAccuracies.get(field);
            fieldAccuracies.put(field, fieldAccuracy.getAccuracy());
        }

        // 计算所有样本的总体准确率
        double allSamplesAccuracy = allSamplesTotalComparisons > 0 ?
                (double) allSamplesMatchedComparisons / allSamplesTotalComparisons : 0;

        return new AccuracyResult(sampleAccuracy.getAccuracy(), fieldAccuracies,
                sampleAccuracy.getTotalComparisons(), sampleAccuracy.getMatchedComparisons(),
                allSamplesAccuracy, allSamplesTotalComparisons, allSamplesMatchedComparisons);
    }

    /**
     * 计算患者信息的准确率并返回不匹配详情
     */
    public String calculatePatientAccuracyWithDetails(JSONObject predictObject, JSONObject labelObject, int sampleIndex) {
        JSONArray jsonArrayLabel = labelObject.getJSONArray("患者信息");
        JSONArray jsonArrayPredict = predictObject.getJSONArray("患者信息");

        StringBuilder detailsOutput = new StringBuilder();
        SampleAccuracy sampleAccuracy = new SampleAccuracy(sampleIndex);
        Map<String, FieldAccuracy> sampleFieldAccuracies = new HashMap<>();

        // 初始化样本字段统计
        String[] fields = FieldConfig.getPatientFields();
        for (String field : fields) {
            sampleFieldAccuracies.put(field, new FieldAccuracy(field));
        }

        // 按照顺序匹配患者信息
        int minSize = Math.min(jsonArrayLabel.size(), jsonArrayPredict.size());

        // 处理共同部分（按顺序一一对应）
        for (int i = 0; i < minSize; i++) {
            JSONObject labelPatient = jsonArrayLabel.getJSONObject(i);
            JSONObject predictPatient = jsonArrayPredict.getJSONObject(i);
            String labelName = labelPatient.getString("患者姓名");

            processPatientFieldsWithOutput(fields, labelPatient, predictPatient, labelName, sampleIndex,
                    sampleAccuracy, sampleFieldAccuracies, detailsOutput);
        }

        // 处理label数量多于predict的情况（多出的部分算作匹配失败）
        for (int i = minSize; i < jsonArrayLabel.size(); i++) {
            JSONObject labelPatient = jsonArrayLabel.getJSONObject(i);
            String labelName = labelPatient.getString("患者姓名");

            processUnmatchedPatientWithOutput(fields, labelPatient, labelName, sampleIndex,
                    sampleAccuracy, sampleFieldAccuracies, detailsOutput);
        }

        return detailsOutput.toString();
    }

    /**
     * 处理患者字段比较
     */
    private void processPatientFields(String[] fields, JSONObject labelPatient, JSONObject predictPatient,
                                      String labelName, int sampleIndex, SampleAccuracy sampleAccuracy,
                                      Map<String, FieldAccuracy> sampleFieldAccuracies) {

        for (String field : fields) {
            String labelValue = JsonUtils.getStringValue(labelPatient, field);
            String predictValue = JsonUtils.getStringValue(predictPatient, field);

            processFieldComparison(field, labelValue, predictValue, sampleIndex, null,
                    labelName, sampleAccuracy, sampleFieldAccuracies, "null");
        }
    }

    /**
     * 处理患者字段比较（带输出）
     */
    private void processPatientFieldsWithOutput(String[] fields, JSONObject labelPatient, JSONObject predictPatient,
                                                String labelName, int sampleIndex, SampleAccuracy sampleAccuracy,
                                                Map<String, FieldAccuracy> sampleFieldAccuracies, StringBuilder detailsOutput) {

        for (String field : fields) {
            String labelValue = JsonUtils.getStringValue(labelPatient, field);
            String predictValue = JsonUtils.getStringValue(predictPatient, field);

            processFieldComparison(field, labelValue, predictValue, sampleIndex, detailsOutput,
                    labelName, sampleAccuracy, sampleFieldAccuracies, "output");
        }
    }

    /**
     * 处理未匹配的患者
     */
    private void processUnmatchedPatient(String[] fields, JSONObject labelPatient, String labelName,
                                         int sampleIndex, SampleAccuracy sampleAccuracy, Map<String, FieldAccuracy> sampleFieldAccuracies) {

        for (String field : fields) {
            String labelValue = JsonUtils.getStringValue(labelPatient, field);
            // 只有非空字段才计入统计
            if (!JsonUtils.isEmptyValue(labelValue)) {
                updateFieldStatistics(field, false, sampleIndex, labelName, labelValue, "未找到匹配",
                        sampleAccuracy, sampleFieldAccuracies);
            }
        }
    }

    /**
     * 处理未匹配的患者（带输出）
     */
    private void processUnmatchedPatientWithOutput(String[] fields, JSONObject labelPatient, String labelName,
                                                   int sampleIndex, SampleAccuracy sampleAccuracy, Map<String, FieldAccuracy> sampleFieldAccuracies,
                                                   StringBuilder detailsOutput) {

        boolean hasUnmatchedFields = false;
        for (String field : fields) {
            String labelValue = JsonUtils.getStringValue(labelPatient, field);
            // 只有非空字段才计入统计
            if (!JsonUtils.isEmptyValue(labelValue)) {
                updateFieldStatistics(field, false, sampleIndex, labelName, labelValue, "未找到匹配",
                        sampleAccuracy, sampleFieldAccuracies);

                // 添加到输出
                if (!hasUnmatchedFields) {
                    if (!detailsOutput.toString().contains("患者 '" + labelName + "' 匹配情况")) {
                        detailsOutput.append("\n患者 '").append(labelName).append("' 匹配情况");
                    }
                    hasUnmatchedFields = true;
                }
                detailsOutput.append("\n    ").append(field).append(": ").append(labelValue).append(" vs 未找到匹配");
            }
        }
    }

    /**
     * 处理字段比较（文档字段）
     */
    private void processFieldComparison(String field, String labelValue, String predictValue,
                                        int sampleIndex, StringBuilder sb) {

        boolean isMatch = FieldMatcher.isFieldMatch(field, labelValue, predictValue);
        boolean shouldCount = shouldCountInStatistics(labelValue, predictValue);

        if (!JsonUtils.isEmptyValue(predictValue) && JsonUtils.isEmptyValue(labelValue)) {
            isMatch = false;
        }

        // 更新统计
        if (shouldCount) {
            updateGlobalStatistics(field, isMatch, sampleIndex, null, labelValue, predictValue, "doc");
        }

        // 添加到输出
        if (sb != null) {
            addToOutput(sb, field, labelValue, predictValue, isMatch, shouldCount);
        }
    }

    /**
     * 处理字段比较（患者字段）
     */
    private void processFieldComparison(String field, String labelValue, String predictValue,
                                        int sampleIndex, StringBuilder sb, String labelName, SampleAccuracy sampleAccuracy,
                                        Map<String, FieldAccuracy> sampleFieldAccuracies, String source) {

        boolean isMatch = FieldMatcher.isFieldMatch(field, labelValue, predictValue);
        boolean shouldCount = shouldCountInStatistics(labelValue, predictValue);

        if (!JsonUtils.isEmptyValue(predictValue) && JsonUtils.isEmptyValue(labelValue)) {
            isMatch = false;
        }

        // 更新统计
        if (shouldCount && "null".equals(source)) {
            updateFieldStatistics(field, isMatch, sampleIndex, labelName, labelValue, predictValue,
                    sampleAccuracy, sampleFieldAccuracies);
            updateGlobalStatistics(field, isMatch, sampleIndex, labelName, labelValue, predictValue, source);
        }

        // 添加到输出
        if (sb != null) {
            addToPatientOutput(sb, field, labelValue, predictValue, isMatch, shouldCount, labelName);
        }
    }

    /**
     * 判断是否应该计入统计
     */
    private boolean shouldCountInStatistics(String labelValue, String predictValue) {
        // 如果两个值都为空，则不计入统计
        return !(JsonUtils.isEmptyValue(labelValue) && JsonUtils.isEmptyValue(predictValue));
    }

    /**
     * 更新字段统计
     */
    private void updateFieldStatistics(String field, boolean isMatch, int sampleIndex, String labelName,
                                       String labelValue, String predictValue, SampleAccuracy sampleAccuracy,
                                       Map<String, FieldAccuracy> sampleFieldAccuracies) {

        FieldAccuracy fieldAccuracy = sampleFieldAccuracies.get(field);
        fieldAccuracy.incrementTotal();
        sampleAccuracy.incrementTotal();

        if (isMatch) {
            fieldAccuracy.incrementMatched();
            sampleAccuracy.incrementMatched();
        } else {
            // 收集错误详情
            String sampleNumber = "样本 #" + (sampleIndex + 1);
            String errorDetail = sampleNumber + ": " + (labelName != null ? labelName + ": " : "") +
                    labelValue + " vs " + (JsonUtils.isEmptyValue(predictValue) ? "未找到匹配" : predictValue);
            fieldAccuracy.addErrorDetail(errorDetail);
        }
    }

    /**
     * 更新全局统计
     */
    private void updateGlobalStatistics(String field, boolean isMatch, int sampleIndex, String labelName,
                                        String labelValue, String predictValue, String source) {
        allSamplesTotalComparisons++;
        FieldAccuracy globalFieldAccuracy = globalFieldAccuracies.get(field);
        globalFieldAccuracy.incrementTotal();

        if (isMatch) {
            allSamplesMatchedComparisons++;
            globalFieldAccuracy.incrementMatched();
        } else {
            // 收集错误详情
            String sampleNumber = "样本 #" + (sampleIndex + 1);
            String errorDetail = sampleNumber + ": " + (labelName != null ? labelName + ": " : "") +
                    labelValue + " vs " + (JsonUtils.isEmptyValue(predictValue) ? "未找到匹配" : predictValue);
            globalFieldAccuracy.addErrorDetail(errorDetail);
        }
    }

    /**
     * 添加到输出（文档字段）
     */
    private void addToOutput(StringBuilder sb, String field, String labelValue, String predictValue,
                             boolean isMatch, boolean shouldCount) {

        // 显示所有不匹配的项，包括预测值为空的情况
        if ((!isMatch && !JsonUtils.isEmptyValue(labelValue) && !JsonUtils.isEmptyValue(predictValue)) ||
                (JsonUtils.isEmptyValue(predictValue) && !JsonUtils.isEmptyValue(labelValue))) {

            if (!sb.toString().contains("文档字段匹配情况")) {
                sb.append("\n文档字段匹配情况");
            }

            String displayPredictValue = JsonUtils.isEmptyValue(predictValue) ? "未找到匹配" : predictValue;
            sb.append("\n    ").append(field).append(": ").append(labelValue).append(" vs ").append(displayPredictValue);
        }
    }

    /**
     * 添加到患者输出
     */
    private void addToPatientOutput(StringBuilder sb, String field, String labelValue, String predictValue,
                                    boolean isMatch, boolean shouldCount, String labelName) {

        // 显示所有不匹配的项，包括预测值为空的情况
        if (!isMatch) {

            if (!sb.toString().contains("患者 '" + labelName + "' 匹配情况")) {
                sb.append("\n患者 '").append(labelName).append("' 匹配情况");
            }

            String displayPredictValue = JsonUtils.isEmptyValue(predictValue) ? "未找到匹配" : predictValue;
            sb.append("\n    ").append(field).append(": ").append(labelValue).append(" vs ").append(displayPredictValue);
        }
    }

    /**
     * 获取全局字段准确率
     */
    public Map<String, FieldAccuracy> getGlobalFieldAccuracies() {
        return new HashMap<>(globalFieldAccuracies);
    }

    /**
     * 获取全局总体准确率
     */
    public double getGlobalAccuracy() {
        return allSamplesTotalComparisons > 0 ?
                (double) allSamplesMatchedComparisons / allSamplesTotalComparisons : 0;
    }

    /**
     * 获取全局总比较数
     */
    public int getGlobalTotalComparisons() {
        return allSamplesTotalComparisons;
    }

    /**
     * 获取全局匹配数
     */
    public int getGlobalMatchedComparisons() {
        return allSamplesMatchedComparisons;
    }

    /**
     * 重置统计数据
     */
    public void reset() {
        allSamplesTotalComparisons = 0;
        allSamplesMatchedComparisons = 0;
        globalFieldAccuracies.clear();
        initializeGlobalFieldAccuracies();
    }
}
