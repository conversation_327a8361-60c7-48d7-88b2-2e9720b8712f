package cn.naii.analysis.matcher;

import cn.hutool.core.io.FileUtil;
import cn.naii.analysis.config.AppConfig;
import cn.naii.analysis.config.FieldConfig;
import cn.naii.analysis.util.JsonUtils;
import cn.naii.analysis.util.StringUtils;
import cn.naii.analysis.util.TimeOfDayComparator;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字段匹配器 - 负责各种字段的匹配逻辑
 * 使用配置文件消除硬编码
 *
 * <AUTHOR>
 */
public class FieldMatcher {
    private static JSONArray technicalItems = null;
    public static JSONArray errorTestItem = new JSONArray();

    static {
        try {
            ClassLoader classLoader = FieldMatcher.class.getClassLoader();
            String file = classLoader.getResource("mapping.json").getFile();
            technicalItems = JSONArray.parseArray(FileUtil.readUtf8String(file));
//            technicalItems = JSONArray.parseArray(FileUtil.readUtf8String("C:\\Users\\<USER>\\Desktop\\mapping.json"));
        } catch (Exception e) {
            System.out.println("读取映射文件失败，" + e.getMessage());
        }
    }

    /**
     * 判断字段值是否匹配
     *
     * @param field        字段名
     * @param labelValue   标签值
     * @param predictValue 预测值
     * @return 是否匹配
     */
    public static boolean isFieldMatch(String field, String labelValue, String predictValue) {
        // 如果两个值都为空，则不计入统计，这里返回值不重要
        // 但为了代码完整性，这里返回 true
        if (JsonUtils.isEmptyValue(labelValue) && JsonUtils.isEmptyValue(predictValue)) {
            return true;
        }

        // 如果预测值为空，标签值不为空，则认为匹配失败
        if (JsonUtils.isEmptyValue(predictValue) && !JsonUtils.isEmptyValue(labelValue)) {
            return false;
        }

        // 如果预测值不为空，标签值为空，则不计入统计，这里返回值不重要，因为调用方会检查这种情况
        // 但为了代码完整性，这里返回 false
        if (!JsonUtils.isEmptyValue(predictValue) && JsonUtils.isEmptyValue(labelValue)) {
            return false;
        }
        // 去除回车
        labelValue = labelValue.replaceAll("[\r\n]", "");
        predictValue = predictValue.replaceAll("[\r\n]", "");
        FieldConfig.FieldType fieldType = FieldConfig.FieldType.fromFieldName(field);
        if (fieldType == null) {
            // 默认字段直接比较字符串
            return labelValue.equals(predictValue);
        }

        return matchByFieldType(fieldType, labelValue, predictValue);
    }

    /**
     * 根据字段类型进行匹配
     */
    private static boolean matchByFieldType(FieldConfig.FieldType fieldType, String labelValue, String predictValue) {
        return switch (fieldType) {
            case BARCODE -> matchBarcode(labelValue, predictValue);
            case PATIENT_NAME -> matchPatientName(labelValue, predictValue);
            case AGE -> matchAge(labelValue, predictValue);
            case GENDER -> matchGender(labelValue, predictValue);
            case TEST_ITEM -> matchTestItem(labelValue, predictValue);
            case DEPARTMENT -> matchContains(labelValue, predictValue);
            case COLLECTION_TIME -> matchCollectionTime(labelValue, predictValue);
            case COLLECTION_DATE -> matchCollectionDate(labelValue, predictValue);
            case BED_NUMBER -> matchBedNumber(labelValue, predictValue);
            case HOSPITAL_NUMBER -> matchHospitalNumber(labelValue, predictValue);
            default -> labelValue.equals(predictValue);
        };
    }

    /**
     * 条形码匹配 - 模糊匹配
     */
    private static boolean matchBarcode(String labelValue, String predictValue) {
//        return predictValue.startsWith(labelValue);
        return predictValue.equals(labelValue);
    }

    /**
     * 患者姓名匹配
     */
    private static boolean matchPatientName(String labelValue, String predictValue) {
        return labelValue.equals(predictValue);
    }

    /**
     * 年龄匹配 - 使用配置的年龄容差
     */
    private static boolean matchAge(String labelValue, String predictValue) {
        try {
            // 处理可能的null或"null"字符串
            if ("null".equalsIgnoreCase(labelValue) || "null".equalsIgnoreCase(predictValue)) {
                return "null".equalsIgnoreCase(labelValue) && "null".equalsIgnoreCase(predictValue);
            }

            int labelAge = Integer.parseInt(labelValue);
            String cleanPredictValue = predictValue;
            String ageSuffix = AppConfig.getTestItemAgeSuffix();
            if (predictValue.indexOf(ageSuffix) > 0) {
                cleanPredictValue = predictValue.substring(0, predictValue.indexOf(ageSuffix));
            }
            int predictAge = Integer.parseInt(cleanPredictValue);
            // 使用配置的年龄容差
            int tolerance = AppConfig.getAgeTolerance();
            return Math.abs(labelAge - predictAge) <= tolerance;
        } catch (NumberFormatException e) {
            // 如果无法解析为数字，则直接比较字符串
            return labelValue.equals(predictValue);
        }
    }

    /**
     * 性别匹配 - 必须完全匹配
     */
    private static boolean matchGender(String labelValue, String predictValue) {
        return labelValue.equals(predictValue);
    }

    /**
     * 床位匹配 - 支持模糊匹配，如"床位1"和"1床"
     */
    private static boolean matchBedNumber(String labelValue, String predictValue) {
        // 提取数字部分进行比较
        String labelNumbers = StringUtils.extractNumbers(labelValue);
        String predictNumbers = StringUtils.extractNumbers(predictValue);

        if (!labelNumbers.isEmpty() && !predictNumbers.isEmpty()) {
            return labelNumbers.equals(predictNumbers);
        }

        // 如果无法提取数字，则直接比较
        return labelValue.equals(predictValue);
    }

    /**
     * 门诊号匹配
     */
    private static boolean matchHospitalNumber(String labelValue, String predictValue) {
        return labelValue.equals(predictValue);
    }

    /**
     * 包含匹配 - 用于科室、医生、样本类型等字段
     */
    private static boolean matchContains(String labelValue, String predictValue) {
        return labelValue.contains(predictValue) || predictValue.contains(labelValue);
    }

    /**
     * 采集时间匹配
     */
    private static boolean matchCollectionTime(String labelValue, String predictValue) {
        String labelValueClean = labelValue.replaceAll("\\s+", "");
        String predictValueClean = predictValue.replaceAll("\\s+", "");

        labelValueClean = parseTimeString(labelValueClean);
        predictValueClean = parseTimeString(predictValueClean);
        if (!labelValueClean.equals(predictValueClean)) {
            return TimeOfDayComparator.compareTimeOfDay(labelValueClean, predictValueClean);
        } else {
            return true;
        }
    }

    /**
     * 采集日期匹配
     */
    private static boolean matchCollectionDate(String labelValue, String predictValue) {
        String labelValueClean = labelValue.replaceAll("\\s+", "");
        String predictValueClean = predictValue.replaceAll("\\s+", "");

        labelValueClean = parseDateString(labelValueClean);
        predictValueClean = parseDateString(predictValueClean);

        return labelValueClean.equals(predictValueClean);
    }

    private static List<Integer> extractNumbers(String str) {
        List<Integer> numbers = new ArrayList<>();
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(str);

        while (matcher.find()) {
            numbers.add(Integer.valueOf(matcher.group()));
        }

        return numbers;
    }

    public static String parseTimeString(String timeStr) {
        // 1. 使用正则表达式提取所有数字部分
        List<Integer> numbers = extractNumbers(timeStr);

        // 2. 判断时间单位是否存在
        boolean hasHour = timeStr.contains("时");
        boolean hasMinute = timeStr.contains("分");

        // 3. 按顺序填充数字到对应位置
        StringBuilder result = new StringBuilder();

        if (hasHour) {
            Integer hour = numbers.isEmpty() ? 0 : numbers.remove(0);
            result.append(hour).append("时");
        }

        if (hasMinute) {
            Integer minute = numbers.isEmpty() ? 0 : numbers.remove(0);
            result.append(minute).append("分");
        }

        // 4. 返回结果字符串
        return result.toString();
    }

    public static String parseDateString(String dateStr) {
        // 1. 使用正则表达式提取所有数字部分
        List<Integer> numbers = extractNumbers(dateStr);

        // 2. 判断时间单位是否存在
        boolean hasYear = dateStr.contains("年");
        boolean hasMonth = dateStr.contains("月");
        boolean hasSecond = dateStr.contains("日");

        // 3. 按顺序填充数字到对应位置
        StringBuilder result = new StringBuilder();

        if (hasYear) {
            Integer hour = numbers.isEmpty() ? 0 : numbers.remove(0);
            result.append(hour).append("年");
        }

        if (hasMonth) {
            Integer minute = numbers.isEmpty() ? 0 : numbers.remove(0);
            result.append(minute).append("月");
        }

        if (hasSecond) {
            Integer minute = numbers.isEmpty() ? 0 : numbers.remove(0);
            result.append(minute).append("日");
        }

        // 4. 返回结果字符串
        return result.toString();
    }

    /**
     * 检测项目匹配 - 使用增强的检测项目匹配算法
     */
    private static boolean matchTestItem(String labelValue, String predictValue) {
        return TestItemMatcher.isTestItemMatch(labelValue, predictValue);
    }

    public static Set<String> getTestItemMatcher(String item) {
        return TestItemMatcher.convertTestItemSet(technicalItems, item);
    }

    /**
     * 检测项目匹配器 - 内部类
     */
    public static class TestItemMatcher {

        /**
         * 判断检测项目是否匹配
         * 实现模糊匹配，只要一个是另一个的子集就算匹配成功
         */
        public static boolean isTestItemMatch(String item1, String item2) {
            // 彻底清理字符串：去掉空格、中文符号、换行等
            String item1forEqual = cleanTestItemString(item1);
            String item2forEqual = cleanTestItemString(item2);

            // 大写数字转成阿拉伯数字，英文字母转小写
            item1forEqual = StringUtils.convertUpperDigitsAndLowerCase(item1forEqual);
            item2forEqual = StringUtils.convertUpperDigitsAndLowerCase(item2forEqual);

//             首先检查是否存在重要的医学概念差异
//            if (hasImportantMedicalDifference(item1, item2)) {
//                return false;
//            }

            // 如果完全相同，直接返回匹配成功
            if (item1forEqual.equals(item2forEqual)) {
                return true;
            }


            // 匹配关键字，映射到标准术语
            Set<String> itemSet1 = convertTestItemSet(technicalItems, item1);
            Set<String> itemSet2 = convertTestItemSet(technicalItems, item2);
            if (itemSet1.isEmpty()) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("predict", item2);
                jsonObject.put("label", item1);
                errorTestItem.add(jsonObject);
                return false;
            }
            String itemSet1Str = String.join(",", itemSet1);
            String itemSet2Str = String.join(",", itemSet2);
            if (itemSet1Str.equals(itemSet2Str) || item1.equals(itemSet1Str) || item2.equals(itemSet1Str)) {
                return true;
            }
//            if (isEnhancedMatch(item1, item2)) {
//                return true;
//            }
//
//            // 将检测项目分解为单个项目列表
//            Set<String> items1 = parseTestItems(item1);
//            Set<String> items2 = parseTestItems(item2);
//
//            // 如果两个列表有交集，则认为匹配成功
//            // 这里的逻辑是：只要有一个项目匹配成功，就认为整个检测项目匹配成功
//            for (String item : items1) {
//                if (items2.contains(item)) {
//                    return true;
//                }
//            }
//
//            // 如果没有交集，则尝试模糊匹配
//            // 对于简写和全称的情况，例如 TSH 和 促甲状腺激素(迈瑞)
//            for (String item1Full : items1) {
//                for (String item2Full : items2) {
//                    // 如果一个包含另一个，则认为匹配成功
//                    if (item1Full.contains(item2Full) || item2Full.contains(item1Full)) {
//                        return true;
//                    }
//                }
//            }
//
//            // 增强的模糊匹配：处理特殊字符和同义词
//            // 例如：抗β2糖蛋白抗体3项 vs 抗β2-糖蛋白|抗体测定3项
//            for (String item1Full : items1) {
//                for (String item2Full : items2) {
//                    if (isEnhancedMatch(item1Full, item2Full)) {
//                        return true;
//                    }
//                }
//            }

            // 如果上述所有匹配方式都失败，则返回匹配失败
            return false;
        }

        public static Set<String> convertTestItemSet(JSONArray technicalTerms, String item) {
            // 字符串预处理
            // 去除所有空格
            item = item.replaceAll("\\s+", "")
                    .replace("\n", "")
                    .replace("\r", "");

            // 中文符号转英文符号
            item = item.replaceAll("，", ",")
                    .replaceAll("（", "(")
                    .replaceAll("）", ")");

            HashSet<String> set = new HashSet<>();
            for (int i = 0; i < technicalTerms.size(); i++) {
                JSONObject technical = technicalTerms.getJSONObject(i);
                List<String> names = technical.getJSONArray("names").stream().map(String::valueOf).toList();
                JSONArray keywords = technical.getJSONArray("keywords");
                for (int j = 0; j < keywords.size(); j++) {
                    String keyword = keywords.getString(j);
                    if (keyword.startsWith("\\")) {
                        if (isMatchRegex(keyword, item)) {
                            set.addAll(names);
                        }
                    } else {
                        if (item.contains(keyword)) {
                            set.addAll(names);
                        }
                    }
                }
            }
            return set;
        }

        private static boolean isMatchRegex(String regex, String str) {
            regex = regex.substring(1, regex.length() - 1);
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(str);
            return matcher.find();
        }

        /**
         * 彻底清理检测项目字符串
         * 去掉空格、中文符号、换行等干扰字符
         */
        public static String cleanTestItemString(String item) {
            if (item == null || item.isEmpty()) {
                return "";
            }

            String cleaned = item;

            // 去掉所有空白字符（空格、制表符、换行符等）
            cleaned = cleaned.replaceAll("\\s+", "");

            // 逐个替换中文标点符号，避免正则表达式问题
            cleaned = cleaned.replace("，", "");
            cleaned = cleaned.replace("。", "");
            cleaned = cleaned.replace("；", "");
            cleaned = cleaned.replace("：", "");
            cleaned = cleaned.replace("！", "");
            cleaned = cleaned.replace("？", "");
            cleaned = cleaned.replace("\u201c", ""); // 左双引号
            cleaned = cleaned.replace("\u201d", ""); // 右双引号
            cleaned = cleaned.replace("\u2018", ""); // 左单引号
            cleaned = cleaned.replace("\u2019", ""); // 右单引号
            cleaned = cleaned.replace("（", "");
            cleaned = cleaned.replace("）", "");
            cleaned = cleaned.replace("【", "");
            cleaned = cleaned.replace("】", "");
            cleaned = cleaned.replace("《", "");
            cleaned = cleaned.replace("》", "");
            cleaned = cleaned.replace("〈", "");
            cleaned = cleaned.replace("〉", "");
            cleaned = cleaned.replace("「", "");
            cleaned = cleaned.replace("」", "");
            cleaned = cleaned.replace("『", "");
            cleaned = cleaned.replace("』", "");
            cleaned = cleaned.replace("〔", "");
            cleaned = cleaned.replace("〕", "");
            cleaned = cleaned.replace("［", "");
            cleaned = cleaned.replace("］", "");
            cleaned = cleaned.replace("｛", "");
            cleaned = cleaned.replace("｝", "");
            cleaned = cleaned.replace("\n", "");
            cleaned = cleaned.replace("\r", "");

            // 去掉英文标点符号（保留一些可能有意义的字符如-）
            cleaned = cleaned.replaceAll("[,\\.;:!?\"'`]", "");

            // 去掉其他常见符号
            cleaned = cleaned.replaceAll("[\\|\\\\/_~\\^]", "");

            return cleaned;
        }

        /**
         * 增强的匹配算法，处理特殊字符和同义词
         * 例如：抗β2糖蛋白抗体3项 vs 抗β2-糖蛋白|抗体测定3项
         */
        private static boolean isEnhancedMatch(String item1, String item2) {
            // 标准化字符串：移除特殊字符和标点符号
            String normalized1 = normalizeTestItem(item1);
            String normalized2 = normalizeTestItem(item2);

            // 完全匹配
            if (normalized1.equals(normalized2)) {
                return true;
            }

            // 包含匹配
            if (normalized1.contains(normalized2) || normalized2.contains(normalized1)) {
                return true;
            }

            // 计算相似度，如果相似度足够高则认为匹配
            double similarity = calculateTestItemSimilarity(normalized1, normalized2);
            return similarity >= 0.8; // 80%相似度阈值
        }

        /**
         * 标准化检测项目字符串
         * 移除特殊字符、标点符号，处理同义词
         */
        private static String normalizeTestItem(String item) {
            if (item == null || item.isEmpty()) {
                return "";
            }

            String normalized = item;

            // 移除常见的特殊字符和标点符号
            normalized = normalized.replaceAll("[\\-|\\(\\)（）\\[\\]【】]", "");

            // 处理同义词替换，但要小心医学概念的区别
            // 只有在特定上下文中才替换"抗体测定"为"抗体"
            if (normalized.contains("抗体测定") && !normalized.contains("抗原")) {
                normalized = normalized.replace("抗体测定", "抗体");
            }

            // 移除一般性的检测相关词汇
            normalized = normalized.replace("检测", "");
            normalized = normalized.replace("测定", "");

            return normalized;
        }

        /**
         * 检查两个项目是否存在重要的医学概念差异
         * 例如：抗原 vs 抗体 是不同的概念，不应该匹配
         */
        private static boolean hasImportantMedicalDifference(String item1, String item2) {
            // 检查是否包含免疫球蛋白标记（IGM, IGG, IGA等），这些通常表示抗体检测
            boolean item1HasImmunoglobulin = containsImmunoglobulin(item1);
            boolean item2HasImmunoglobulin = containsImmunoglobulin(item2);

            // 如果包含免疫球蛋白标记，则认为是抗体检测，允许匹配
            if (item1HasImmunoglobulin || item2HasImmunoglobulin) {
                return false; // 不认为有重要差异
            }

            // 更精确的抗原和抗体检查
            // 检查是否一个只有抗原，另一个只有抗体
            boolean item1OnlyAntigen = item1.contains("抗原") && !item1.contains("抗体");
            boolean item1OnlyAntibody = item1.contains("抗体") && !item1.contains("抗原");
            boolean item2OnlyAntigen = item2.contains("抗原") && !item2.contains("抗体");
            boolean item2OnlyAntibody = item2.contains("抗体") && !item2.contains("抗原");

            // 如果一个只有抗原，另一个只有抗体，则认为有重要差异
            if ((item1OnlyAntigen && item2OnlyAntibody) || (item1OnlyAntibody && item2OnlyAntigen)) {
                return true;
            }

            // 特殊情况：如果一个是"XX抗原"，另一个是"XX抗原抗体"，也认为有差异
            // 因为"XX抗原抗体"通常指的是针对XX抗原的抗体检测
            if (item1OnlyAntigen && item2.contains("抗原抗体")) {
                // 检查是否是同一个抗原的不同检测
                String antigenName = extractAntigenName(item1);
                if (antigenName != null && item2.contains(antigenName + "抗原抗体")) {
                    return true;
                }
            }
            if (item2OnlyAntigen && item1.contains("抗原抗体")) {
                String antigenName = extractAntigenName(item2);
                if (antigenName != null && item1.contains(antigenName + "抗原抗体")) {
                    return true;
                }
            }

            // 可以在这里添加更多的医学概念差异检查
            // 例如：定性 vs 定量、急性 vs 慢性等

            return false;
        }

        /**
         * 检查是否包含免疫球蛋白标记
         * IGM, IGG, IGA等通常表示抗体检测
         */
        private static boolean containsImmunoglobulin(String text) {
            if (text == null) {
                return false;
            }

            String upperText = text.toUpperCase();
            return upperText.contains("IGM") || upperText.contains("IGG") ||
                    upperText.contains("IGA") || upperText.contains("IGE");
        }

        /**
         * 从抗原名称中提取前缀
         * 例如："乙肝表面抗原" -> "乙肝表面"
         */
        private static String extractAntigenName(String antigenText) {
            if (antigenText == null || !antigenText.contains("抗原")) {
                return null;
            }

            int index = antigenText.indexOf("抗原");
            if (index > 0) {
                return antigenText.substring(0, index);
            }

            return null;
        }

        /**
         * 计算两个检测项目的相似度
         * 基于字符匹配度计算
         */
        private static double calculateTestItemSimilarity(String item1, String item2) {
            if (item1 == null || item2 == null || item1.isEmpty() || item2.isEmpty()) {
                return 0.0;
            }

            // 计算最长公共子序列长度
            int lcsLength = longestCommonSubsequence(item1, item2);
            int maxLength = Math.max(item1.length(), item2.length());

            return maxLength > 0 ? (double) lcsLength / maxLength : 0.0;
        }

        /**
         * 计算最长公共子序列长度
         */
        private static int longestCommonSubsequence(String str1, String str2) {
            int m = str1.length();
            int n = str2.length();
            int[][] dp = new int[m + 1][n + 1];

            for (int i = 1; i <= m; i++) {
                for (int j = 1; j <= n; j++) {
                    if (str1.charAt(i - 1) == str2.charAt(j - 1)) {
                        dp[i][j] = dp[i - 1][j - 1] + 1;
                    } else {
                        dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1]);
                    }
                }
            }

            return dp[m][n];
        }

        /**
         * 将检测项目字符串分解为单个项目列表 - 使用配置的分隔符
         */
        private static Set<String> parseTestItems(String testItems) {
            Set<String> result = new HashSet<>();

            // 使用配置的分隔符
            String mainSeparator = AppConfig.getTestItemMainSeparator();
            String itemsSeparator = AppConfig.getTestItemItemsSeparator();

            // 获取配置的特殊字符分隔符，包括|,-等
            String specialCharacters = AppConfig.getProperty("matching.special.characters", "|,-");

            // 分割主要部分和简写部分
            String[] parts = testItems.split(mainSeparator);

            if (parts.length > 0) {
                // 处理主要部分，例如：游离三碳甲状腺原氨酸(迈瑞),游离甲状腺素(迈瑞),促甲状腺激素(迈瑞)
                String mainPart = parts[0];

                // 先按配置的分隔符分割
                String[] mainItems = mainPart.split(itemsSeparator);
                for (String item : mainItems) {
                    // 再按特殊字符分割，处理如"抗β2-糖蛋白|抗体测定3项"这样的情况
                    String[] subItems = splitBySpecialCharacters(item.trim(), specialCharacters);
                    for (String subItem : subItems) {
                        if (!subItem.trim().isEmpty()) {
                            result.add(subItem.trim());
                        }
                    }
                }

                // 处理简写部分，例如：FT3,FT4,TSH
                if (parts.length > 1) {
                    String abbreviationPart = parts[1];
                    String[] abbreviationItems = abbreviationPart.split(itemsSeparator);
                    for (String item : abbreviationItems) {
                        String[] subItems = splitBySpecialCharacters(item.trim(), specialCharacters);
                        for (String subItem : subItems) {
                            if (!subItem.trim().isEmpty()) {
                                result.add(subItem.trim());
                            }
                        }
                    }
                }
            }

            return result;
        }

        /**
         * 按特殊字符分割字符串
         *
         * @param text              要分割的文本
         * @param specialCharacters 特殊字符字符串，如"|,-"
         * @return 分割后的字符串数组
         */
        private static String[] splitBySpecialCharacters(String text, String specialCharacters) {
            if (text == null || text.isEmpty() || specialCharacters == null || specialCharacters.isEmpty()) {
                return new String[]{text};
            }

            // 构建正则表达式，转义特殊字符
            StringBuilder regex = new StringBuilder("[");
            for (char c : specialCharacters.toCharArray()) {
                // 转义正则表达式中的特殊字符
                if ("\\^$.|?*+()[]{}".indexOf(c) != -1) {
                    regex.append("\\").append(c);
                } else {
                    regex.append(c);
                }
            }
            regex.append("]");

            return text.split(regex.toString());
        }
    }
}
