package cn.naii.analysis.util;

import cn.naii.analysis.config.AppConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字符串工具类 - 使用配置文件消除硬编码
 */
public class StringUtils {

    /**
     * 计算两个字符串的编辑距离（Levenshtein距离）
     *
     * @param s1 字符串1
     * @param s2 字符串2
     * @return 编辑距离
     */
    public static int calculateLevenshteinDistance(String s1, String s2) {
        if (s1 == null || s2 == null) {
            return Math.max(s1 == null ? 0 : s1.length(), s2 == null ? 0 : s2.length());
        }

        int[][] dp = new int[s1.length() + 1][s2.length() + 1];

        for (int i = 0; i <= s1.length(); i++) {
            dp[i][0] = i;
        }

        for (int j = 0; j <= s2.length(); j++) {
            dp[0][j] = j;
        }

        for (int i = 1; i <= s1.length(); i++) {
            for (int j = 1; j <= s2.length(); j++) {
                int cost = (s1.charAt(i - 1) == s2.charAt(j - 1)) ? 0 : 1;
                dp[i][j] = Math.min(Math.min(dp[i - 1][j] + 1, dp[i][j - 1] + 1), dp[i - 1][j - 1] + cost);
            }
        }

        return dp[s1.length()][s2.length()];
    }

    /**
     * 判断两个姓名是否相似 - 使用配置的相似度阈值
     *
     * @param name1 姓名1
     * @param name2 姓名2
     * @return 是否相似
     */
    public static boolean isSimilarName(String name1, String name2) {
        if (name1 == null || name2 == null) {
            return false;
        }

        // 完全相同
        if (name1.equals(name2)) {
            return true;
        }

        // 计算编辑距离，使用配置的相似度阈值
        int distance = calculateLevenshteinDistance(name1, name2);
        int maxLength = Math.max(name1.length(), name2.length());
        double threshold = AppConfig.getNameSimilarityThreshold();
        return distance <= maxLength * threshold;
    }

    /**
     * 提取字符串中的数字部分 - 使用配置的床位匹配模式
     *
     * @param str 输入字符串
     * @return 数字字符串，如果没有数字则返回空字符串
     */
    public static String extractNumbers(String str) {
        if (str == null) {
            return "";
        }
        List<String> integers = new ArrayList<>();
        Pattern pattern = Pattern.compile("([+]?\\d+)");
        Matcher matcher = pattern.matcher(str);
        while (matcher.find()) {
            integers.add(matcher.group(1));
        }
        return integers.size() == 1 ? integers.get(0) : "";
    }

    /**
     * 判断字符串是否为纯数字
     *
     * @param str 输入字符串
     * @return 是否为纯数字
     */
    public static boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        return str.matches("\\d+");
    }

    /**
     * 将字符串中的大写数字转换为阿拉伯数字，并将英文字母转换为小写
     * 使用正则表达式和替换方法实现
     *
     * @param input 输入字符串
     * @return 转换后的字符串
     */
    public static String convertUpperDigitsAndLowerCase(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        
        // 先将英文字母转为小写
        String result = input.toLowerCase();
        
        // 使用正则表达式替换中文数字
        // 大写数字替换
        result = result.replaceAll("零", "0")
                      .replaceAll("一", "1")
                      .replaceAll("二", "2")
                      .replaceAll("三", "3")
                      .replaceAll("四", "4")
                      .replaceAll("五", "5")
                      .replaceAll("六", "6")
                      .replaceAll("七", "7")
                      .replaceAll("八", "8")
                      .replaceAll("九", "9");
        
        // 大写金额数字替换
        result = result.replaceAll("壹", "1")
                      .replaceAll("贰", "2")
                      .replaceAll("叁", "3")
                      .replaceAll("肆", "4")
                      .replaceAll("伍", "5")
                      .replaceAll("陆", "6")
                      .replaceAll("柒", "7")
                      .replaceAll("捌", "8")
                      .replaceAll("玖", "9")
                      .replaceAll("拾", "0");
        
        return result;
    }
}
