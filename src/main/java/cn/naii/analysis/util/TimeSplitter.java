package cn.naii.analysis.util;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

public class TimeSplitter {

    /**
     * 将 JSONObject 中的所有患者信息对象的 "采集时间" 字段拆分为 "采集日期" 和 "采集时间"
     * @param jsonObject 输入的 JSON 对象
     * @return 处理后的 JSON 对象
     */
    public static JSONObject splitCollectionTime(JSONObject jsonObject) {
        // 检查是否包含患者信息数组
        if (!jsonObject.containsKey("患者信息")) {
            return jsonObject;
        }

        // 获取患者信息数组
        JSONArray patientInfos = jsonObject.getJSONArray("患者信息");

        // 遍历所有患者信息对象
        for (int i = 0; i < patientInfos.size(); i++) {
            JSONObject patientInfo = patientInfos.getJSONObject(i);
            processPatientInfo(patientInfo);
        }

        return jsonObject;
    }

    /**
     * 处理单个患者信息对象
     */
    private static void processPatientInfo(JSONObject patientInfo) {
        // 检查是否包含采集时间字段
        if (!patientInfo.containsKey("采集时间")) {
            // 添加空的采集日期和采集时间字段
            patientInfo.put("采集日期", null);
            patientInfo.put("采集时间", null);
            return;
        }

        // 获取采集时间字段的值
        String collectionTime = patientInfo.getString("采集时间");

        // 如果采集时间为空，直接添加空的采集日期和采集时间字段
        if (collectionTime == null || collectionTime.trim().isEmpty()) {
            patientInfo.put("采集日期", null);
            patientInfo.put("采集时间", null);
            return;
        }

        // 去除首尾空格
        collectionTime = collectionTime.trim();

        // 按空格分割
        String[] parts = collectionTime.split("\\s+", 2);

        // 根据分割结果设置采集日期和采集时间
        if (parts.length == 2) {
            patientInfo.put("采集日期", parts[0]);
            patientInfo.put("采集时间", parts[1]);
        } else {
            // 如果只有一个部分，尝试判断是日期还是时间
            if (isDate(parts[0])) {
                patientInfo.put("采集日期", parts[0]);
                patientInfo.put("采集时间", null);
            } else if (isTime(parts[0])) {
                patientInfo.put("采集日期", null);
                patientInfo.put("采集时间", parts[0]);
            } else {
                // 无法判断，全部放入采集时间
                patientInfo.put("采集日期", null);
                patientInfo.put("采集时间", parts[0]);
            }
        }

//        // 移除原始的采集时间字段
//        patientInfo.remove("采集时间");
    }

    /**
     * 判断字符串是否为日期格式（包含年月日）
     */
    private static boolean isDate(String str) {
        // 简单判断：包含年、月、日、- 或 / 字符
        return str.contains("年") || str.contains("月") || str.contains("日");
    }

    /**
     * 判断字符串是否为时间格式（包含时分）
     */
    private static boolean isTime(String str) {
        // 简单判断：包含时、分、: 或 . 字符
        return str.contains("时") || str.contains("分");
    }
}