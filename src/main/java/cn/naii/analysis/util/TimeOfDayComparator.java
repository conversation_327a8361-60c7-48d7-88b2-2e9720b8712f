package cn.naii.analysis.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TimeOfDayComparator {

    // 常见的时分秒格式列表
    private static final List<String> COMMON_TIME_FORMATS = new ArrayList<String>() {{
        // 数字格式
        add("HH:mm:ss");
        add("HH:mm");
        add("H:mm:ss");
        add("H:mm");
        add("hh:mm:ss a");
        add("hh:mm a");
        add("h:mm:ss a");
        add("h:mm a");
        add("HHmmss");
        add("HHmm");

        // 带中文的格式
        add("HH时mm分ss秒");
        add("HH时mm分");
        add("H时mm分ss秒");
        add("H时mm分");
        add("上午h时mm分ss秒");
        add("下午h时mm分ss秒");
        add("上午h时mm分");
        add("下午h时mm分");
    }};

    /**
     * 比较两个时分秒字符串（不含日期）
     * @param time1 第一个时间字符串
     * @param time2 第二个时间字符串
     * @return 如果time1早于time2返回-1，如果相等返回0，如果time1晚于time2返回1
     * @throws ParseException 如果两个时间字符串都无法解析
     */
    public static boolean compareTimeOfDay(String time1, String time2) {
        // 解析时间，获取毫秒数（基于同一天）
        long milliseconds1 = parseTimeOfDay(time1);
        long milliseconds2 = parseTimeOfDay(time2);

        if (milliseconds1 == -1 || milliseconds2 == -1) {
           return false;
        }

        return milliseconds1 == milliseconds2;
    }

    /**
     * 尝试解析时分秒字符串，返回当天的毫秒数
     * @param timeString 时间字符串
     * @return 解析后的毫秒数（从当天00:00:00开始计算），解析失败返回-1
     */
    private static long parseTimeOfDay(String timeString) {
        if (timeString == null || timeString.trim().isEmpty()) {
            return -1;
        }

        String normalized = normalizeTimeString(timeString.trim());

        // 尝试每种格式解析
        for (String format : COMMON_TIME_FORMATS) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(format);
                sdf.setLenient(false); // 严格模式
                Date date = sdf.parse(normalized);

                // 计算从00:00:00开始的毫秒数
                return (date.getHours() * 3600 + date.getMinutes() * 60 + date.getSeconds()) * 1000L;
            } catch (ParseException e) {
                // 解析失败，尝试下一种格式
                continue;
            }
        }

        // 尝试正则表达式提取
        return parseWithRegex(normalized);
    }

    /**
     * 标准化时间字符串，统一一些常见的变体
     */
    private static String normalizeTimeString(String timeString) {
        // 替换全角字符为半角
        timeString = timeString.replace('：', ':')
                .replace('．', '.')
                .replace('，', ',');

        // 统一上午下午的表达方式
        timeString = timeString.replace("早上", "上午")
                .replace("早晨", "上午")
                .replace("中午", "下午")
                .replace("傍晚", "下午")
                .replace("晚上", "下午")
                .replace("凌晨", "上午");

        // 移除可能的空格
        return timeString.replaceAll("\\s+", "");
    }

    /**
     * 使用正则表达式提取时分秒信息
     */
    private static long parseWithRegex(String timeString) {
        // 匹配小时、分钟、秒的模式
        Pattern pattern = Pattern.compile(
                "(?i)(上午|下午|am|pm)?\\D*" +
                        "(\\d{1,2})\\D*" +
                        "(\\d{1,2})?\\D*" +
                        "(\\d{1,2})?"
        );

        Matcher matcher = pattern.matcher(timeString);
        if (matcher.find()) {
            try {
                String period = matcher.group(1);
                int hour = Integer.parseInt(matcher.group(2));
                int minute = matcher.group(3) != null ? Integer.parseInt(matcher.group(3)) : 0;
                int second = matcher.group(4) != null ? Integer.parseInt(matcher.group(4)) : 0;

                // 处理12小时制
                if (period != null) {
                    period = period.toLowerCase();
                    if (period.contains("下午") || period.contains("pm")) {
                        if (hour < 12) hour += 12;
                    } else { // 上午或am
                        if (hour == 12) hour = 0;
                    }
                }

                // 验证时间有效性
                if (hour >= 0 && hour < 24 && minute >= 0 && minute < 60 && second >= 0 && second < 60) {
                    return (hour * 3600 + minute * 60 + second) * 1000L;
                }
            } catch (NumberFormatException e) {
                // 数字转换失败
            }
        }

        return -1;
    }

    // 测试方法
    public static void main(String[] args) {
        try {
            testComparison("14:50", "14时50分", 0);

        } catch (ParseException e) {
            e.printStackTrace();
        }
    }

    private static void testComparison(String time1, String time2, int expected) throws ParseException {
        boolean result = compareTimeOfDay(time1, time2);
        System.out.printf("%s 与 %s 比较: %b (预期: %d) %s%n",
                time1, time2, result, expected, result ? "成功" : "失败");
    }
}

