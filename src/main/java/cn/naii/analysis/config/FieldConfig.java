package cn.naii.analysis.config;

import lombok.Getter;

/**
 * 字段配置类 - 定义所有用于分析的字段
 * 使用配置文件消除硬编码
 * <AUTHOR>
 */
public class FieldConfig {

    /**
     * 患者信息字段定义 - 从配置文件获取
     */
    public static final String[] PATIENT_FIELDS = {
        AppConfig.getProperty("field.barcode", "条形码"),
        AppConfig.getProperty("field.patient.name", "患者姓名"),
        AppConfig.getProperty("field.age", "年龄"),
        AppConfig.getProperty("field.gender", "性别"),
        AppConfig.getProperty("field.hospital.number", "住院/门诊号"),
        AppConfig.getProperty("field.bed.number", "床位"),
        AppConfig.getProperty("field.department", "科室"),
        AppConfig.getProperty("field.sample.type", "样本类型"),
        AppConfig.getProperty("field.sample.status", "样本性状"),
        AppConfig.getProperty("field.collection.time", "采集时间"),
        AppConfig.getProperty("field.doctor", "医生"),
        AppConfig.getProperty("field.test.item", "检测项目"),
        AppConfig.getProperty("field.remarks", "备注栏"),
        AppConfig.getProperty("field.region", "病区"),
        AppConfig.getProperty("field.collection.date", "采集日期"),
//        AppConfig.getProperty("field.diagnose", "诊断")
    };

    /**
     * 预测对象中的文档字段
     */
    public static final String[] PREDICT_DOCUMENT_FIELDS = {
        AppConfig.getProperty("field.date", "日期")
    };

    /**
     * 标签对象中的文档字段
     */
    public static final String[] LABEL_DOCUMENT_FIELDS = {
        AppConfig.getProperty("field.date", "日期")
    };

    /**
     * 需要特殊处理的字段类型 - 使用配置文件
     */
    @Getter
    public enum FieldType {
        BARCODE(AppConfig.getProperty("field.barcode", "条形码")),
        PATIENT_NAME(AppConfig.getProperty("field.patient.name", "患者姓名")),
        AGE(AppConfig.getProperty("field.age", "年龄")),
        GENDER(AppConfig.getProperty("field.gender", "性别")),
        HOSPITAL_NUMBER(AppConfig.getProperty("field.hospital.number", "住院/门诊号")),
        BED_NUMBER(AppConfig.getProperty("field.bed.number", "床位")),
        DEPARTMENT(AppConfig.getProperty("field.department", "科室")),
        SAMPLE_TYPE(AppConfig.getProperty("field.sample.type", "样本类型")),
        SAMPLE_STATUS(AppConfig.getProperty("field.sample.status", "样本性状")),
        COLLECTION_TIME(AppConfig.getProperty("field.collection.time", "采集时间")),
        DOCTOR(AppConfig.getProperty("field.doctor", "医生")),
        TEST_ITEM(AppConfig.getProperty("field.test.item", "检测项目")),
        REMARKS(AppConfig.getProperty("field.remarks", "备注栏")),
        DATE(AppConfig.getProperty("field.date", "日期")),
        REGION(AppConfig.getProperty("field.region", "病区")),
        COLLECTION_DATE(AppConfig.getProperty("field.collection.date", "采集日期"));

        private final String fieldName;

        FieldType(String fieldName) {
            this.fieldName = fieldName;
        }

        public static FieldType fromFieldName(String fieldName) {
            for (FieldType type : values()) {
                if (type.fieldName.equals(fieldName)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 获取所有患者字段
     */
    public static String[] getPatientFields() {
        return PATIENT_FIELDS.clone();
    }

    /**
     * 获取预测文档字段
     */
    public static String[] getPredictDocumentFields() {
        return PREDICT_DOCUMENT_FIELDS.clone();
    }

    /**
     * 获取标签文档字段
     */
    public static String[] getLabelDocumentFields() {
        return LABEL_DOCUMENT_FIELDS.clone();
    }
}
