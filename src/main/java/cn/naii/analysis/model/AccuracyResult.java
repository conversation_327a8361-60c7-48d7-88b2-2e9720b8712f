package cn.naii.analysis.model;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * Model class to represent accuracy calculation results
 * <AUTHOR>
 */
@Getter
public class AccuracyResult {
    private final double overallAccuracy;
    private final Map<String, Double> fieldAccuracies;
    private final int totalComparisons;
    private final int matchedComparisons;
    // 所有样本的总体准确率
    @Setter
    private double allSamplesAccuracy;
    // 所有样本的总比较项数
    @Setter
    private int allSamplesTotalComparisons;
    // 所有样本的匹配项数
    @Setter
    private int allSamplesMatchedComparisons;

    public AccuracyResult(double overallAccuracy, Map<String, Double> fieldAccuracies,
                         int totalComparisons, int matchedComparisons) {
        this.overallAccuracy = overallAccuracy;
        this.fieldAccuracies = fieldAccuracies;
        this.totalComparisons = totalComparisons;
        this.matchedComparisons = matchedComparisons;
        this.allSamplesAccuracy = 0;
        this.allSamplesTotalComparisons = 0;
        this.allSamplesMatchedComparisons = 0;
    }

    public AccuracyResult(double overallAccuracy, Map<String, Double> fieldAccuracies,
                         int totalComparisons, int matchedComparisons,
                         double allSamplesAccuracy, int allSamplesTotalComparisons, int allSamplesMatchedComparisons) {
        this.overallAccuracy = overallAccuracy;
        this.fieldAccuracies = fieldAccuracies;
        this.totalComparisons = totalComparisons;
        this.matchedComparisons = matchedComparisons;
        this.allSamplesAccuracy = allSamplesAccuracy;
        this.allSamplesTotalComparisons = allSamplesTotalComparisons;
        this.allSamplesMatchedComparisons = allSamplesMatchedComparisons;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("准确率统计结果:\n");
        sb.append(String.format("当前样本准确率: %.2f%% (%d/%d)\n", overallAccuracy * 100, matchedComparisons, totalComparisons));

        // 添加所有样本的总体准确率
//        if (allSamplesTotalComparisons > 0) {
//            sb.append(String.format("所有样本总体准确率: %.2f%% (%d/%d)\n",
//                    allSamplesAccuracy * 100, allSamplesMatchedComparisons, allSamplesTotalComparisons));
//        }

        sb.append("各字段准确率:\n");

        fieldAccuracies.forEach((field, accuracy) -> {
            sb.append(String.format("- %s: %.2f%%\n", field, accuracy * 100));
        });

        return sb.toString();
    }
}
