# \u5e94\u7528\u914d\u7f6e\u6587\u4ef6
# \u6570\u636e\u6587\u4ef6\u914d\u7f6e
#data.file.path=src/main/resources/json/new/generated_predictions20250611_with_pyzbar_barcode7B.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250611_with_pyzbar_barcode7Bparam.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions_split_data_time.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250611_ROLMOCR.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250611_bad2good_ROLMOCR_rl6e-4_no_prompt_update.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250619_ROLMOCR_rl6e-4.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250619_ROLMOCR_rl6e-4_only_10_sample.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250619_ROLMOCR_rl6e-4_only_10_sample_with_pyzbar_barcode7Bparam_datetime_together.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250619_ROLMOCR_rl6e-4_only_10_sample_0623_with_pyzbar_barcode7Bparam_datetime_together.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250619_ROLMOCR_rl6e-4_with_pyzbar_barcode7Bparam_datetime_together.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250619_ROLMOCR_rl6e-4_with_pyzbar_barcode7Bparam_datetime_together_test.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250623_ROLMOCR_rl6e-4.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250623_ROLMOCR_rl6e-4_with_pyzbar_barcode7Bparam_datetime_together.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250619_ROLMOCR_rl6e-4_with_single_recognize.jsonl
data.file.path=src/main/resources/json/v10/output_20250726_095205.jsonl
#data.file.path=src/main/resources/json/new/best_until_now_with_single_recognize_8bit.jsonl
#data.file.path=src/main/resources/json/new/best_until_now_with_single_recognize_8bit_before.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250623_ROLMOCR_rl6e-4_with_single_recognize_8bit.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250703_ROLMOCR_rl6e-4_1260.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250703_ROLMOCR_rl6e-4.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250707_ROLMOCR_rl6e-4.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250707_ROLMOCR_rl6e-4_with_single_recognize_8bit.jsonl
#data.file.path=src/main/resources/json/new/3B.jsonl
#data.file.path=src/main/resources/json/new/batch1.jsonl
#data.file.path=src/main/resources/json/new/batch1_modify_word.jsonl
#data.file.path=src/main/resources/json/new/batch1_modify_label.jsonl
#data.file.path=src/main/resources/json/new/batch1_modify_item.jsonl
#data.file.path=src/main/resources/json/new/batch_model_batch_data_single_recog.jsonl
#data.file.path=src/main/resources/json/new/batch_model_007pic.jsonl
#data.file.path=src/main/resources/json/new/batch_model_007pic_with_pyzbar_barcode7Bparam.jsonl
#data.file.path=src/main/resources/json/new/007+008batch.jsonl
#data.file.path=src/main/resources/json/new/007+008batch_model_007pic.jsonl
#data.file.path=src/main/resources/json/new/alone_no_label.jsonl
#data.file.path=src/main/resources/json/new/all_5e-4.jsonl
#data.file.path=src/main/resources/json/new/all_single_recog.jsonl
#data.file.path=src/main/resources/json/new/007+008batch_new.jsonl
#data.file.path=src/main/resources/json/new/007+008batch_base_007.jsonl
#data.file.path=src/main/resources/json/new/all.jsonl
#data.file.path=src/main/resources/json/new/007+008batch_model_008pic.jsonl
#data.file.path=src/main/resources/json/new/007+008batch_model_007pic_with_pyzbar_barcode7Bparam.jsonl
#data.file.path=src/main/resources/json/new/007+008batch_model_008batchpic_with_pyzbar_barcode7Bparam.jsonl
#data.file.path=src/main/resources/json/new/008batch_no_label.jsonl
#data.file.path=src/main/resources/json/new/007_small_lr_008batch.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250619_MNIST_HWDB_ROLMOCR_rl6e-4.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250611_bad2good_ROLMOCR_rl6e-4.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250611_ROLMOCR_rl6e-4.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250611_ROLMOCR_rl6e-4_with_pyzbar_barcode7Bparam.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250611_ROLMOCR_rl6e-4_with_pyzbar_barcode7Bparam_datetime.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250611_ROLMOCR_rl6e-4_with_pyzbar_barcode7Bparam_datetime_together.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250611_only_good_ROLMOCR_rl6e-4.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250611_only_good_ROLMOCR_rl6e-4_with_pyzbar_barcode7Bparam.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250611_only_good_ROLMOCR_rl6e-4_with_pyzbar_barcode7Bparam_datetime.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions20250611_only_good_ROLMOCR_rl6e-4_with_pyzbar_barcode7Bparam_datetime_together.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions_split_data_time_with_pyzbar_barcode7Bparam(delete_bad).jsonl
#data.file.path=src/main/resources/json/new/generated_predictions_split_data_time_with_pyzbar_barcode7Bparam.jsonl
#data.file.path=src/main/resources/json/new/generated_predictions_dataset2_with_pyzbar_barcode7B.jsonl
data.file.encoding=UTF-8
data.file.truncate.length=100

# \u5339\u914d\u89c4\u5219\u914d\u7f6e
matching.age.tolerance=0
matching.name.similarity.threshold=0.33
matching.character.match.threshold=0.5
matching.special.characters=|,-

# \u8f93\u51fa\u914d\u7f6e
output.sample.header.template============== \u6837\u672c #{0} ==============
output.error.sample.header.template============== \u9519\u8bef\u6837\u672c #{0} ==============
output.label.info.template=label \u771f\u5b9e\u6570\u636e\u7ed3\u679c:
output.predict.info.template=predict \u6a21\u578b\u8bc6\u522b\u7ed3\u679c:
output.accuracy.analysis.header=\u51c6\u786e\u7387\u5206\u6790\u7ed3\u679c:
output.document.field.header=\u6587\u6863\u5b57\u6bb5\u5339\u914d\u60c5\u51b5
output.patient.match.template=\u60a3\u8005 '{0}' \u5339\u914d\u60c5\u51b5
output.patient.not.found.template=\u60a3\u8005 '{0}' \u672a\u627e\u5230\u5339\u914d
output.field.accuracy.header=\u6309\u5b57\u6bb5\u7684\u51c6\u786e\u7387:
output.sample.error.header=\u6309\u6837\u672c\u5206\u7ec4\u7684\u9519\u8bef\u8be6\u60c5:
output.overall.accuracy.template=\u6240\u6709\u6837\u672c\u603b\u4f53\u51c6\u786e\u7387: {0}% ({1}/{2})
output.field.accuracy.template=- {0}: {1}% ({2}/{3}) \u9519\u8bef\u6570: {4}
output.error.detail.header=\u9519\u8bef\u8be6\u60c5:
output.separator.line=------------------------------------------------
output.section.separator=================================================

# \u9519\u8bef\u6d88\u606f\u914d\u7f6e
error.json.parse=Error parsing JSON: {0}
error.file.read=Error reading file: {0}
error.line.parse=Error parsing JSON line: {0}
error.line.content=Line content (truncated): {0}
error.field.not.found=\u672a\u627e\u5230\u5339\u914d
error.empty.value.label=\u7a7a\u503c(\u53ea\u5728\u6807\u7b7e\u4e2d\u5b58\u5728)
error.both.empty=\u4e24\u4e2a\u503c\u90fd\u4e3a\u7a7a(\u4e0d\u8ba1\u5165\u7edf\u8ba1)
error.label.empty=\u6807\u7b7e\u503c\u4e3a\u7a7a(\u5339\u914d\u5931\u8d25)
error.predict.empty=\u9884\u6d4b\u503c\u4e3a\u7a7a(\u5339\u914d\u5931\u8d25)
error.match.success=\u5339\u914d
error.match.failure=\u4e0d\u5339\u914d

# \u5b57\u6bb5\u540d\u79f0\u914d\u7f6e
field.barcode=\u6761\u5f62\u7801
field.patient.name=\u60a3\u8005\u59d3\u540d
field.age=\u5e74\u9f84
field.gender=\u6027\u522b
field.hospital.number=\u4f4f\u9662/\u95e8\u8bca\u53f7
field.bed.number=\u5e8a\u4f4d
field.department=\u79d1\u5ba4
field.sample.type=\u6837\u672c\u7c7b\u578b
field.sample.status=\u6837\u672c\u6027\u72b6
field.collection.time=\u91c7\u96c6\u65f6\u95f4
field.doctor=\u533b\u751f
field.test.item=\u68c0\u6d4b\u9879\u76ee
field.remarks=\u5907\u6ce8\u680f
field.date=\u65e5\u671f
field.image.path=\u56fe\u7247\u5730\u5740

# JSON\u5b57\u6bb5\u540d\u79f0\u914d\u7f6e
json.field.patient.info=\u60a3\u8005\u4fe1\u606f
json.field.predict=predict
json.field.label=label

# \u68c0\u6d4b\u9879\u76ee\u5339\u914d\u914d\u7f6e
test.item.separator.main= \u6216\u8005\u662f 
test.item.separator.items=,
test.item.age.suffix=\u5c81

# \u65f6\u95f4\u5339\u914d\u914d\u7f6e
time.separator=:
time.compare.part=0

# \u5e8a\u4f4d\u5339\u914d\u914d\u7f6e
bed.number.pattern=\\d+

# \u6587\u4ef6\u8def\u5f84\u5206\u9694\u7b26
file.path.separator.windows=\\
file.path.separator.unix=/
file.extension.separator=.

# \u7a7a\u503c\u6807\u8bc6\u7b26
empty.values=null,/,

# \u8f93\u51fa\u7edf\u8ba1\u4fe1\u606f
stats.total.predictions=Total predictions read: {0}
stats.sample.predictions=Sample of predictions:
